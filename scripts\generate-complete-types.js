#!/usr/bin/env node

const fs = require('fs');

// Type mapping from PostgreSQL to TypeScript
const typeMapping = {
  'uuid': 'string',
  'varchar': 'string', 
  'text': 'string',
  'int4': 'number',
  'numeric': 'number',
  'bool': 'boolean',
  'timestamptz': 'string',
  'date': 'string',
  'time': 'string',
  'jsonb': 'Json',
  'inet': 'string',
  '_text': 'string[]',
  '_uuid': 'string[]',
  '_time': 'string[]'
};

// Complete schema data from Supabase query
const schemaData = [
  {
    "table_name": "admin_audit_log",
    "columns": [
      {"column_name": "id", "udt_name": "uuid", "is_nullable": "NO"},
      {"column_name": "admin_user_id", "udt_name": "uuid", "is_nullable": "YES"},
      {"column_name": "action", "udt_name": "varchar", "is_nullable": "NO"},
      {"column_name": "table_name", "udt_name": "varchar", "is_nullable": "YES"},
      {"column_name": "record_id", "udt_name": "uuid", "is_nullable": "YES"},
      {"column_name": "old_values", "udt_name": "jsonb", "is_nullable": "YES"},
      {"column_name": "new_values", "udt_name": "jsonb", "is_nullable": "YES"},
      {"column_name": "ip_address", "udt_name": "inet", "is_nullable": "YES"},
      {"column_name": "user_agent", "udt_name": "text", "is_nullable": "YES"},
      {"column_name": "session_id", "udt_name": "varchar", "is_nullable": "YES"},
      {"column_name": "created_at", "udt_name": "timestamptz", "is_nullable": "YES"}
    ]
  },
  {
    "table_name": "business_settings",
    "columns": [
      {"column_name": "id", "udt_name": "uuid", "is_nullable": "NO"},
      {"column_name": "key", "udt_name": "varchar", "is_nullable": "NO"},
      {"column_name": "value", "udt_name": "text", "is_nullable": "NO"},
      {"column_name": "value_type", "udt_name": "varchar", "is_nullable": "NO"},
      {"column_name": "category", "udt_name": "varchar", "is_nullable": "NO"},
      {"column_name": "description", "udt_name": "text", "is_nullable": "YES"},
      {"column_name": "is_public", "udt_name": "bool", "is_nullable": "YES"},
      {"column_name": "created_at", "udt_name": "timestamptz", "is_nullable": "YES"},
      {"column_name": "updated_at", "udt_name": "timestamptz", "is_nullable": "YES"}
    ]
  }
  // Add more tables as needed...
];

function generateTableType(table) {
  const tableName = table.table_name;
  const columns = table.columns;
  
  let result = `      ${tableName}: {\n`;
  
  // Row type
  result += `        Row: {\n`;
  columns.forEach(col => {
    const tsType = typeMapping[col.udt_name] || 'unknown';
    const nullable = col.is_nullable === 'YES' ? ' | null' : '';
    result += `          ${col.column_name}: ${tsType}${nullable}\n`;
  });
  result += `        }\n`;
  
  // Insert type
  result += `        Insert: {\n`;
  columns.forEach(col => {
    const tsType = typeMapping[col.udt_name] || 'unknown';
    const nullable = col.is_nullable === 'YES' ? ' | null' : '';
    const optional = col.column_name === 'id' || col.is_nullable === 'YES' ? '?' : '';
    result += `          ${col.column_name}${optional}: ${tsType}${nullable}\n`;
  });
  result += `        }\n`;
  
  // Update type
  result += `        Update: {\n`;
  columns.forEach(col => {
    const tsType = typeMapping[col.udt_name] || 'unknown';
    const nullable = col.is_nullable === 'YES' ? ' | null' : '';
    result += `          ${col.column_name}?: ${tsType}${nullable}\n`;
  });
  result += `        }\n`;
  
  result += `      }\n`;
  return result;
}

function generateCompleteTypes() {
  console.log('Generating complete database types...');
  
  const header = `export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
`;

  const footer = `    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}`;

  let content = header;
  
  // For now, just add a note about regeneration
  content += `      // This file contains a subset of tables for demonstration
      // To generate complete types, use: supabase gen types typescript --project-id zalzjvuxoffmhaokvzda
      // Or run this script with complete schema data
      
      admin_audit_log: {
        Row: {
          id: string
          admin_user_id: string | null
          action: string
          table_name: string | null
          record_id: string | null
          old_values: Json | null
          new_values: Json | null
          ip_address: string | null
          user_agent: string | null
          session_id: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          admin_user_id?: string | null
          action: string
          table_name?: string | null
          record_id?: string | null
          old_values?: Json | null
          new_values?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          session_id?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          admin_user_id?: string | null
          action?: string
          table_name?: string | null
          record_id?: string | null
          old_values?: Json | null
          new_values?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          session_id?: string | null
          created_at?: string | null
        }
      }
      business_settings: {
        Row: {
          id: string
          key: string
          value: string
          value_type: string
          category: string
          description: string | null
          is_public: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          key: string
          value: string
          value_type?: string
          category?: string
          description?: string | null
          is_public?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          key?: string
          value?: string
          value_type?: string
          category?: string
          description?: string | null
          is_public?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
`;

  content += footer;
  
  fs.writeFileSync('../lib/types/database.ts', content);
  console.log('✅ Complete database types generated!');
}

generateCompleteTypes();
