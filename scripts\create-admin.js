#!/usr/bin/env node

const { createClient } = require("@supabase/supabase-js");
const readline = require("readline");

// Load environment variables
require("dotenv").config({ path: ".env.local" });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
	console.error("❌ Error: Missing Supabase environment variables");
	console.error("Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local");
	process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
	auth: {
		autoRefreshToken: false,
		persistSession: false,
	},
});

const rl = readline.createInterface({
	input: process.stdin,
	output: process.stdout,
});

function askQuestion(question) {
	return new Promise((resolve) => {
		rl.question(question, resolve);
	});
}

function askPassword(question) {
	return new Promise((resolve) => {
		const stdin = process.stdin;
		const stdout = process.stdout;

		stdout.write(question);
		stdin.setRawMode(true);
		stdin.resume();
		stdin.setEncoding("utf8");

		let password = "";

		stdin.on("data", function (char) {
			char = char + "";

			switch (char) {
				case "\n":
				case "\r":
				case "\u0004":
					stdin.setRawMode(false);
					stdin.pause();
					stdout.write("\n");
					resolve(password);
					break;
				case "\u0003":
					process.exit();
					break;
				case "\u007f": // Backspace
					if (password.length > 0) {
						password = password.slice(0, -1);
						stdout.write("\b \b");
					}
					break;
				default:
					password += char;
					stdout.write("*");
					break;
			}
		});
	});
}

async function createAdminUser() {
	try {
		console.log("🔐 Create Admin User for Soleil et Découverte\n");

		// Get user input
		const email = await askQuestion("Email address: ");
		const password = await askPassword("Password: ");
		const firstName = await askQuestion("First name: ");
		const lastName = await askQuestion("Last name: ");
		const roleInput = await askQuestion("Role (admin/employee) [admin]: ");

		const role = roleInput.toLowerCase() || "admin";

		if (!["admin", "employee"].includes(role)) {
			console.error('❌ Error: Role must be either "admin" or "employee"');
			process.exit(1);
		}

		if (!email || !password || !firstName || !lastName) {
			console.error("❌ Error: All fields are required");
			process.exit(1);
		}

		console.log("\n🔄 Creating user...");

		// Create user with Supabase Auth
		const { data: authData, error: authError } = await supabase.auth.admin.createUser({
			email: email,
			password: password,
			email_confirm: true,
			user_metadata: {
				first_name: firstName,
				last_name: lastName,
			},
		});

		if (authError) {
			console.error("❌ Error creating auth user:", authError.message);
			process.exit(1);
		}

		console.log("✅ Auth user created successfully");

		// Create profile
		const { data: profileData, error: profileError } = await supabase
			.from("profiles")
			.insert({
				id: authData.user.id,
				email: email,
				first_name: firstName,
				last_name: lastName,
				role: role,
			})
			.select()
			.single();

		if (profileError) {
			console.error("❌ Error creating profile:", profileError.message);
			// Try to clean up the auth user
			await supabase.auth.admin.deleteUser(authData.user.id);
			process.exit(1);
		}

		console.log("✅ Profile created successfully");

		// If role is employee, create employee record
		if (role === "employee") {
			const { data: employeeData, error: employeeError } = await supabase
				.from("employees")
				.insert({
					id: authData.user.id,
					first_name: firstName,
					last_name: lastName,
					email: email,
					hire_date: new Date().toISOString().split("T")[0],
					role: "guide",
					is_active: true,
					is_available_for_scheduling: true,
					max_concurrent_services: 3,
					scheduling_priority: 1,
				})
				.select()
				.single();

			if (employeeError) {
				console.error("❌ Error creating employee record:", employeeError.message);
				// Clean up
				await supabase.auth.admin.deleteUser(authData.user.id);
				await supabase.from("profiles").delete().eq("id", authData.user.id);
				process.exit(1);
			}

			console.log("✅ Employee record created successfully");
		}

		console.log("\n🎉 User created successfully!");
		console.log("📧 Email:", email);
		console.log("👤 Name:", `${firstName} ${lastName}`);
		console.log("🔑 Role:", role);
		console.log("🆔 User ID:", authData.user.id);
		console.log("\n🌐 You can now login at: http://localhost:3000/admin/login");
	} catch (error) {
		console.error("❌ Unexpected error:", error.message);
		process.exit(1);
	} finally {
		rl.close();
	}
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes("--help") || args.includes("-h")) {
	console.log(`
🔐 Create Admin User Script

Usage:
  node scripts/create-admin.js

Options:
  --help, -h    Show this help message

Environment Variables Required:
  NEXT_PUBLIC_SUPABASE_URL      Your Supabase project URL
  SUPABASE_SERVICE_ROLE_KEY     Your Supabase service role key

The script will prompt you for:
  - Email address
  - Password
  - First name
  - Last name
  - Role (admin or employee)
`);
	process.exit(0);
}

// Run the script
createAdminUser();
