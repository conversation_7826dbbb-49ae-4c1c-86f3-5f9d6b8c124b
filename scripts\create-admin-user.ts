import { supabaseAdmin } from '../lib/supabase'

async function createAdminUser() {
  try {
    console.log('Creating admin user...')

    // Create user with Supabase Auth
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'admin123',
      email_confirm: true,
      user_metadata: {
        first_name: 'Admin',
        last_name: 'User'
      }
    })

    if (authError) {
      console.error('Error creating auth user:', authError)
      return
    }

    console.log('Auth user created:', authData.user?.email)

    // Create profile
    const { data: profileData, error: profileError } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: authData.user!.id,
        email: authData.user!.email!,
        first_name: 'Admin',
        last_name: 'User',
        role: 'admin'
      })
      .select()
      .single()

    if (profileError) {
      console.error('Error creating profile:', profileError)
      return
    }

    console.log('Profile created:', profileData)

    // Create employee user
    const { data: empAuthData, error: empAuthError } = await supabaseAdmin.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'employee123',
      email_confirm: true,
      user_metadata: {
        first_name: 'Employee',
        last_name: 'User'
      }
    })

    if (empAuthError) {
      console.error('Error creating employee auth user:', empAuthError)
      return
    }

    console.log('Employee auth user created:', empAuthData.user?.email)

    // Create employee profile
    const { data: empProfileData, error: empProfileError } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: empAuthData.user!.id,
        email: empAuthData.user!.email!,
        first_name: 'Employee',
        last_name: 'User',
        role: 'employee'
      })
      .select()
      .single()

    if (empProfileError) {
      console.error('Error creating employee profile:', empProfileError)
      return
    }

    console.log('Employee profile created:', empProfileData)

    // Create employee record
    const { data: employeeData, error: employeeError } = await supabaseAdmin
      .from('employees')
      .insert({
        id: empAuthData.user!.id,
        first_name: 'Employee',
        last_name: 'User',
        email: empAuthData.user!.email!,
        hire_date: new Date().toISOString().split('T')[0],
        role: 'guide',
        is_active: true,
        is_available_for_scheduling: true,
        max_concurrent_services: 3,
        scheduling_priority: 1
      })
      .select()
      .single()

    if (employeeError) {
      console.error('Error creating employee record:', employeeError)
      return
    }

    console.log('Employee record created:', employeeData)

    console.log('\n✅ Test users created successfully!')
    console.log('\nAdmin Login:')
    console.log('Email: <EMAIL>')
    console.log('Password: admin123')
    console.log('\nEmployee Login:')
    console.log('Email: <EMAIL>')
    console.log('Password: employee123')

  } catch (error) {
    console.error('Error creating admin user:', error)
  }
}

// Run the script
createAdminUser()
