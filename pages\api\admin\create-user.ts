import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'
import { Database } from '../../../lib/types/database'

// Admin endpoint to create users - should be protected in production
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { email, password, firstName, lastName, role = 'admin' } = req.body

  if (!email || !password) {
    return res.status(400).json({ error: 'Email and password are required' })
  }

  try {
    // Use service role key for admin operations
    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Create user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
        role
      }
    })

    if (authError) {
      console.error('Auth error:', authError)
      return res.status(400).json({ error: authError.message })
    }

    if (!authData.user) {
      return res.status(400).json({ error: 'Failed to create user' })
    }

    // Create profile record
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        email,
        first_name: firstName || null,
        last_name: lastName || null,
        role
      })

    if (profileError) {
      console.error('Profile error:', profileError)
      // Try to clean up the auth user if profile creation fails
      await supabase.auth.admin.deleteUser(authData.user.id)
      return res.status(400).json({ error: 'Failed to create user profile' })
    }

    // If creating an employee, also create employee record
    if (role === 'employee') {
      const { error: employeeError } = await supabase
        .from('employees')
        .insert({
          id: authData.user.id,
          first_name: firstName || null,
          last_name: lastName || null,
          email,
          hire_date: new Date().toISOString().split('T')[0],
          is_active: true,
          role
        })

      if (employeeError) {
        console.error('Employee error:', employeeError)
        // Continue anyway - profile was created successfully
      }
    }

    res.status(201).json({
      message: 'User created successfully',
      user: {
        id: authData.user.id,
        email,
        firstName,
        lastName,
        role
      }
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}
