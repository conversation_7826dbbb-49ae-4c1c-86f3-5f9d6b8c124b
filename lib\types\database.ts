export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export interface Database {
	public: {
		Tables: {
			profiles: {
				Row: {
					id: string;
					email: string;
					first_name: string | null;
					last_name: string | null;
					phone: string | null;
					role: "admin" | "employee" | "customer";
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id: string;
					email: string;
					first_name?: string | null;
					last_name?: string | null;
					phone?: string | null;
					role?: "admin" | "employee" | "customer";
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					email?: string;
					first_name?: string | null;
					last_name?: string | null;
					phone?: string | null;
					role?: "admin" | "employee" | "customer";
					created_at?: string;
					updated_at?: string;
				};
			};
			services: {
				Row: {
					id: string;
					name: string;
					description: string | null;
					duration_minutes: number;
					buffer_time_minutes: number;
					base_price: number;
					max_participants: number;
					min_age: number;
					max_age: number | null;
					is_family_friendly: boolean;
					is_active: boolean;
					image_url: string | null;
					default_employee_id: string | null;
					requires_qualification: boolean;
					auto_assign_employees: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					name: string;
					description?: string | null;
					duration_minutes: number;
					buffer_time_minutes?: number;
					base_price: number;
					max_participants: number;
					min_age?: number;
					max_age?: number | null;
					is_family_friendly?: boolean;
					is_active?: boolean;
					image_url?: string | null;
					default_employee_id?: string | null;
					requires_qualification?: boolean;
					auto_assign_employees?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					name?: string;
					description?: string | null;
					duration_minutes?: number;
					buffer_time_minutes?: number;
					base_price?: number;
					max_participants?: number;
					min_age?: number;
					max_age?: number | null;
					is_family_friendly?: boolean;
					is_active?: boolean;
					image_url?: string | null;
					default_employee_id?: string | null;
					requires_qualification?: boolean;
					auto_assign_employees?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			employees: {
				Row: {
					id: string;
					first_name: string;
					last_name: string;
					email: string;
					phone: string | null;
					role: string;
					skills: string[] | null;
					languages: string[] | null;
					is_active: boolean;
					default_hourly_rate: number | null;
					is_available_for_scheduling: boolean;
					max_concurrent_services: number;
					scheduling_priority: number;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					first_name: string;
					last_name: string;
					email: string;
					phone?: string | null;
					role: string;
					skills?: string[] | null;
					languages?: string[] | null;
					is_active?: boolean;
					default_hourly_rate?: number | null;
					is_available_for_scheduling?: boolean;
					max_concurrent_services?: number;
					scheduling_priority?: number;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					first_name?: string;
					last_name?: string;
					email?: string;
					phone?: string | null;
					role?: string;
					skills?: string[] | null;
					languages?: string[] | null;
					is_active?: boolean;
					default_hourly_rate?: number | null;
					is_available_for_scheduling?: boolean;
					max_concurrent_services?: number;
					scheduling_priority?: number;
					created_at?: string;
					updated_at?: string;
				};
			};
			customers: {
				Row: {
					id: string;
					email: string;
					first_name: string;
					last_name: string;
					phone: string | null;
					emergency_contact_name: string | null;
					emergency_contact_phone: string | null;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					email: string;
					first_name: string;
					last_name: string;
					phone?: string | null;
					emergency_contact_name?: string | null;
					emergency_contact_phone?: string | null;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					email?: string;
					first_name?: string;
					last_name?: string;
					phone?: string | null;
					emergency_contact_name?: string | null;
					emergency_contact_phone?: string | null;
					created_at?: string;
					updated_at?: string;
				};
			};
			equipment: {
				Row: {
					id: string;
					name: string;
					description: string | null;
					total_capacity: number;
					capacity_per_participant: number;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					name: string;
					description?: string | null;
					total_capacity?: number;
					capacity_per_participant?: number;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					name?: string;
					description?: string | null;
					total_capacity?: number;
					capacity_per_participant?: number;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			pricing_tiers: {
				Row: {
					id: string;
					service_id: string;
					tier_name: string;
					min_age: number;
					max_age: number | null;
					price: number;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					service_id: string;
					tier_name: string;
					min_age?: number;
					max_age?: number | null;
					price: number;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					service_id?: string;
					tier_name?: string;
					min_age?: number;
					max_age?: number | null;
					price?: number;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			employee_availability: {
				Row: {
					id: string;
					employee_id: string;
					day_of_week: number;
					start_time: string;
					end_time: string;
					is_available: boolean;
					effective_from: string | null;
					effective_until: string | null;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					employee_id: string;
					day_of_week: number;
					start_time: string;
					end_time: string;
					is_available?: boolean;
					effective_from?: string | null;
					effective_until?: string | null;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					employee_id?: string;
					day_of_week?: number;
					start_time?: string;
					end_time?: string;
					is_available?: boolean;
					effective_from?: string | null;
					effective_until?: string | null;
					created_at?: string;
					updated_at?: string;
				};
			};
			employee_time_off: {
				Row: {
					id: string;
					employee_id: string;
					start_date: string;
					end_date: string;
					start_time: string | null;
					end_time: string | null;
					reason: string | null;
					type: string;
					status: string;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					employee_id: string;
					start_date: string;
					end_date: string;
					start_time?: string | null;
					end_time?: string | null;
					reason?: string | null;
					type?: string;
					status?: string;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					employee_id?: string;
					start_date?: string;
					end_date?: string;
					start_time?: string | null;
					end_time?: string | null;
					reason?: string | null;
					type?: string;
					status?: string;
					created_at?: string;
					updated_at?: string;
				};
			};

			reservations: {
				Row: {
					id: string;
					customer_id: string;
					service_id: string;
					assigned_employee_id: string | null;
					start_time: string;
					end_time: string;
					reservation_number: string;
					participant_count: number;
					total_amount: number;
					currency: string;
					status: "pending" | "confirmed" | "cancelled" | "completed" | "no_show";
					special_requests: string | null;
					discount_code: string | null;
					discount_amount: number;
					check_in_time: string | null;
					qr_code: string | null;
					booking_source: string;
					admin_notes: string | null;
					requires_confirmation: boolean;
					confirmed_at: string | null;
					confirmed_by: string | null;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					customer_id: string;
					service_id: string;
					assigned_employee_id?: string | null;
					start_time: string;
					end_time: string;
					reservation_number: string;
					participant_count?: number;
					total_amount: number;
					currency?: string;
					status?: "pending" | "confirmed" | "cancelled" | "completed" | "no_show";
					special_requests?: string | null;
					discount_code?: string | null;
					discount_amount?: number;
					check_in_time?: string | null;
					qr_code?: string | null;
					booking_source?: string;
					admin_notes?: string | null;
					requires_confirmation?: boolean;
					confirmed_at?: string | null;
					confirmed_by?: string | null;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					customer_id?: string;
					service_id?: string;
					assigned_employee_id?: string | null;
					start_time?: string;
					end_time?: string;
					reservation_number?: string;
					participant_count?: number;
					total_amount?: number;
					currency?: string;
					status?: "pending" | "confirmed" | "cancelled" | "completed" | "no_show";
					special_requests?: string | null;
					discount_code?: string | null;
					discount_amount?: number;
					check_in_time?: string | null;
					qr_code?: string | null;
					booking_source?: string;
					admin_notes?: string | null;
					requires_confirmation?: boolean;
					confirmed_at?: string | null;
					confirmed_by?: string | null;
					created_at?: string;
					updated_at?: string;
				};
			};
			service_scheduling_rules: {
				Row: {
					id: string;
					service_id: string;
					day_of_week: number | null;
					min_advance_booking_hours: number;
					max_advance_booking_days: number;
					operating_start_time: string | null;
					operating_end_time: string | null;
					booking_interval_minutes: number | null;
					specific_times: string[] | null;
					max_bookings_per_day: number | null;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					service_id: string;
					day_of_week?: number | null;
					min_advance_booking_hours?: number;
					max_advance_booking_days?: number;
					operating_start_time?: string | null;
					operating_end_time?: string | null;
					booking_interval_minutes?: number | null;
					specific_times?: string[] | null;
					max_bookings_per_day?: number | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					service_id?: string;
					day_of_week?: number | null;
					min_advance_booking_hours?: number;
					max_advance_booking_days?: number;
					operating_start_time?: string | null;
					operating_end_time?: string | null;
					booking_interval_minutes?: number | null;
					specific_times?: string[] | null;
					max_bookings_per_day?: number | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			service_blackout_dates: {
				Row: {
					id: string;
					service_id: string;
					start_date: string;
					end_date: string;
					reason: string | null;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					service_id: string;
					start_date: string;
					end_date: string;
					reason?: string | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					service_id?: string;
					start_date?: string;
					end_date?: string;
					reason?: string | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			employee_service_qualifications: {
				Row: {
					id: string;
					employee_id: string;
					service_id: string;
					qualification_level: string;
					certified_date: string | null;
					expiry_date: string | null;
					notes: string | null;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					employee_id: string;
					service_id: string;
					qualification_level?: string;
					certified_date?: string | null;
					expiry_date?: string | null;
					notes?: string | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					employee_id?: string;
					service_id?: string;
					qualification_level?: string;
					certified_date?: string | null;
					expiry_date?: string | null;
					notes?: string | null;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			schedule_templates: {
				Row: {
					id: string;
					name: string;
					description: string | null;
					service_id: string;
					template_data: any;
					is_active: boolean;
					created_at: string;
					updated_at: string;
				};
				Insert: {
					id?: string;
					name: string;
					description?: string | null;
					service_id: string;
					template_data: any;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
				Update: {
					id?: string;
					name?: string;
					description?: string | null;
					service_id?: string;
					template_data?: any;
					is_active?: boolean;
					created_at?: string;
					updated_at?: string;
				};
			};
			admin_audit_log: {
				Row: {
					id: string;
					admin_user_id: string | null;
					action: string;
					table_name: string | null;
					record_id: string | null;
					old_values: any | null;
					new_values: any | null;
					ip_address: string | null;
					user_agent: string | null;
					session_id: string | null;
					created_at: string;
				};
				Insert: {
					id?: string;
					admin_user_id?: string | null;
					action: string;
					table_name?: string | null;
					record_id?: string | null;
					old_values?: any | null;
					new_values?: any | null;
					ip_address?: string | null;
					user_agent?: string | null;
					session_id?: string | null;
					created_at?: string;
				};
				Update: {
					id?: string;
					admin_user_id?: string | null;
					action?: string;
					table_name?: string | null;
					record_id?: string | null;
					old_values?: any | null;
					new_values?: any | null;
					ip_address?: string | null;
					user_agent?: string | null;
					session_id?: string | null;
					created_at?: string;
				};
			};
		};
		Views: {
			[_ in never]: never;
		};
		Functions: {
			[_ in never]: never;
		};
		Enums: {
			[_ in never]: never;
		};
	};
}
