export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export interface Database {
	public: {
		Tables: {
			admin_audit_log: {
				Row: {
					id: string;
					admin_user_id: string | null;
					action: string;
					table_name: string | null;
					record_id: string | null;
					old_values: Json | null;
					new_values: Json | null;
					ip_address: string | null;
					user_agent: string | null;
					session_id: string | null;
					created_at: string | null;
				};
				Insert: {
					id?: string;
					admin_user_id?: string | null;
					action: string;
					table_name?: string | null;
					record_id?: string | null;
					old_values?: Json | null;
					new_values?: Json | null;
					ip_address?: string | null;
					user_agent?: string | null;
					session_id?: string | null;
					created_at?: string | null;
				};
				Update: {
					id?: string;
					admin_user_id?: string | null;
					action?: string;
					table_name?: string | null;
					record_id?: string | null;
					old_values?: Json | null;
					new_values?: Json | null;
					ip_address?: string | null;
					user_agent?: string | null;
					session_id?: string | null;
					created_at?: string | null;
				};
			};
			business_settings: {
				Row: {
					id: string;
					key: string;
					value: string;
					value_type: string;
					category: string;
					description: string | null;
					is_public: boolean | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					key: string;
					value: string;
					value_type?: string;
					category?: string;
					description?: string | null;
					is_public?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					key?: string;
					value?: string;
					value_type?: string;
					category?: string;
					description?: string | null;
					is_public?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			customer_analytics: {
				Row: {
					id: string;
					customer_id: string;
					total_reservations: number | null;
					completed_reservations: number | null;
					cancelled_reservations: number | null;
					no_show_reservations: number | null;
					total_spent: number | null;
					total_participants: number | null;
					average_rating: number | null;
					total_reviews: number | null;
					first_reservation_date: string | null;
					last_reservation_date: string | null;
					favorite_service_id: string | null;
					preferred_time_slot: string | null;
					average_group_size: number | null;
					customer_lifetime_value: number | null;
					loyalty_tier: string | null;
					last_updated: string | null;
					created_at: string | null;
				};
				Insert: {
					id?: string;
					customer_id: string;
					total_reservations?: number | null;
					completed_reservations?: number | null;
					cancelled_reservations?: number | null;
					no_show_reservations?: number | null;
					total_spent?: number | null;
					total_participants?: number | null;
					average_rating?: number | null;
					total_reviews?: number | null;
					first_reservation_date?: string | null;
					last_reservation_date?: string | null;
					favorite_service_id?: string | null;
					preferred_time_slot?: string | null;
					average_group_size?: number | null;
					customer_lifetime_value?: number | null;
					loyalty_tier?: string | null;
					last_updated?: string | null;
					created_at?: string | null;
				};
				Update: {
					id?: string;
					customer_id?: string;
					total_reservations?: number | null;
					completed_reservations?: number | null;
					cancelled_reservations?: number | null;
					no_show_reservations?: number | null;
					total_spent?: number | null;
					total_participants?: number | null;
					average_rating?: number | null;
					total_reviews?: number | null;
					first_reservation_date?: string | null;
					last_reservation_date?: string | null;
					favorite_service_id?: string | null;
					preferred_time_slot?: string | null;
					average_group_size?: number | null;
					customer_lifetime_value?: number | null;
					loyalty_tier?: string | null;
					last_updated?: string | null;
					created_at?: string | null;
				};
			};
			customer_feedback: {
				Row: {
					id: string;
					reservation_id: string;
					customer_id: string;
					rating: number;
					review_text: string | null;
					service_quality_rating: number | null;
					staff_rating: number | null;
					equipment_rating: number | null;
					would_recommend: boolean | null;
					is_public: boolean | null;
					response_text: string | null;
					responded_by: string | null;
					responded_at: string | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					reservation_id: string;
					customer_id: string;
					rating: number;
					review_text?: string | null;
					service_quality_rating?: number | null;
					staff_rating?: number | null;
					equipment_rating?: number | null;
					would_recommend?: boolean | null;
					is_public?: boolean | null;
					response_text?: string | null;
					responded_by?: string | null;
					responded_at?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					reservation_id?: string;
					customer_id?: string;
					rating?: number;
					review_text?: string | null;
					service_quality_rating?: number | null;
					staff_rating?: number | null;
					equipment_rating?: number | null;
					would_recommend?: boolean | null;
					is_public?: boolean | null;
					response_text?: string | null;
					responded_by?: string | null;
					responded_at?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			customer_journey_events: {
				Row: {
					id: string;
					customer_id: string;
					event_type: string;
					event_data: Json | null;
					reservation_id: string | null;
					service_id: string | null;
					session_id: string | null;
					user_agent: string | null;
					ip_address: string | null;
					referrer_url: string | null;
					created_at: string | null;
				};
				Insert: {
					id?: string;
					customer_id: string;
					event_type: string;
					event_data?: Json | null;
					reservation_id?: string | null;
					service_id?: string | null;
					session_id?: string | null;
					user_agent?: string | null;
					ip_address?: string | null;
					referrer_url?: string | null;
					created_at?: string | null;
				};
				Update: {
					id?: string;
					customer_id?: string;
					event_type?: string;
					event_data?: Json | null;
					reservation_id?: string | null;
					service_id?: string | null;
					session_id?: string | null;
					user_agent?: string | null;
					ip_address?: string | null;
					referrer_url?: string | null;
					created_at?: string | null;
				};
			};
			customers: {
				Row: {
					id: string;
					date_of_birth: string | null;
					nationality: string | null;
					emergency_contact_name: string | null;
					emergency_contact_phone: string | null;
					dietary_restrictions: string | null;
					medical_conditions: string | null;
					marketing_consent: boolean | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id: string;
					date_of_birth?: string | null;
					nationality?: string | null;
					emergency_contact_name?: string | null;
					emergency_contact_phone?: string | null;
					dietary_restrictions?: string | null;
					medical_conditions?: string | null;
					marketing_consent?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					date_of_birth?: string | null;
					nationality?: string | null;
					emergency_contact_name?: string | null;
					emergency_contact_phone?: string | null;
					dietary_restrictions?: string | null;
					medical_conditions?: string | null;
					marketing_consent?: boolean | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			daily_business_metrics: {
				Row: {
					id: string;
					metric_date: string;
					total_reservations: number | null;
					confirmed_reservations: number | null;
					cancelled_reservations: number | null;
					total_revenue: number | null;
					total_participants: number | null;
					new_customers: number | null;
					repeat_customers: number | null;
					average_order_value: number | null;
					occupancy_rate: number | null;
					customer_satisfaction: number | null;
					weather_condition: string | null;
					special_events: string[] | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					metric_date: string;
					total_reservations?: number | null;
					confirmed_reservations?: number | null;
					cancelled_reservations?: number | null;
					total_revenue?: number | null;
					total_participants?: number | null;
					new_customers?: number | null;
					repeat_customers?: number | null;
					average_order_value?: number | null;
					occupancy_rate?: number | null;
					customer_satisfaction?: number | null;
					weather_condition?: string | null;
					special_events?: string[] | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					metric_date?: string;
					total_reservations?: number | null;
					confirmed_reservations?: number | null;
					cancelled_reservations?: number | null;
					total_revenue?: number | null;
					total_participants?: number | null;
					new_customers?: number | null;
					repeat_customers?: number | null;
					average_order_value?: number | null;
					occupancy_rate?: number | null;
					customer_satisfaction?: number | null;
					weather_condition?: string | null;
					special_events?: string[] | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			discount_coupons: {
				Row: {
					id: string;
					code: string;
					description: string | null;
					discount_type: string;
					discount_value: number;
					min_purchase_amount: number | null;
					max_discount_amount: number | null;
					usage_limit: number | null;
					current_usage: number | null;
					valid_from: string;
					valid_until: string;
					applicable_services: string[] | null;
					is_active: boolean | null;
					created_by: string | null;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id?: string;
					code: string;
					description?: string | null;
					discount_type: string;
					discount_value: number;
					min_purchase_amount?: number | null;
					max_discount_amount?: number | null;
					usage_limit?: number | null;
					current_usage?: number | null;
					valid_from: string;
					valid_until: string;
					applicable_services?: string[] | null;
					is_active?: boolean | null;
					created_by?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					code?: string;
					description?: string | null;
					discount_type?: string;
					discount_value?: number;
					min_purchase_amount?: number | null;
					max_discount_amount?: number | null;
					usage_limit?: number | null;
					current_usage?: number | null;
					valid_from?: string;
					valid_until?: string;
					applicable_services?: string[] | null;
					is_active?: boolean | null;
					created_by?: string | null;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
			// Note: Additional tables (employees, equipment, services, etc.) exist in the database
			// This is a simplified version focusing on the business_settings table
			// For complete types, regenerate using Supabase CLI: supabase gen types typescript
			profiles: {
				Row: {
					id: string;
					email: string;
					first_name: string | null;
					last_name: string | null;
					phone: string | null;
					role: string;
					created_at: string | null;
					updated_at: string | null;
				};
				Insert: {
					id: string;
					email: string;
					first_name?: string | null;
					last_name?: string | null;
					phone?: string | null;
					role?: string;
					created_at?: string | null;
					updated_at?: string | null;
				};
				Update: {
					id?: string;
					email?: string;
					first_name?: string | null;
					last_name?: string | null;
					phone?: string | null;
					role?: string;
					created_at?: string | null;
					updated_at?: string | null;
				};
			};
		};
		Views: {
			[_ in never]: never;
		};
		Functions: {
			[_ in never]: never;
		};
		Enums: {
			[_ in never]: never;
		};
	};
}
