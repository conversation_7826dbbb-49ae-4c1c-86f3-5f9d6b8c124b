const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

function loadEnvFile() {
	const envPath = path.join(process.cwd(), ".env.local");
	if (!fs.existsSync(envPath)) {
		console.error("❌ .env.local file not found");
		process.exit(1);
	}

	const envContent = fs.readFileSync(envPath, "utf8");
	const lines = envContent.split("\n");

	for (const line of lines) {
		const trimmed = line.trim();
		if (trimmed && !trimmed.startsWith("#")) {
			const [key, ...valueParts] = trimmed.split("=");
			if (key && valueParts.length > 0) {
				const value = valueParts.join("=").replace(/^["']|["']$/g, "");
				process.env[key] = value;
			}
		}
	}
}

async function createAdmin() {
	console.log("🚀 Creating admin user...");

	// Load environment variables
	loadEnvFile();

	const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
	const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

	if (!supabaseUrl || !serviceRoleKey) {
		console.error("❌ Missing environment variables:");
		console.error("   NEXT_PUBLIC_SUPABASE_URL:", !!supabaseUrl);
		console.error("   SUPABASE_SERVICE_ROLE_KEY:", !!serviceRoleKey);
		process.exit(1);
	}

	// Admin user details
	const adminEmail = "<EMAIL>";
	const adminPassword = "admin123";
	const firstName = "Admin";
	const lastName = "User";

	try {
		// Create Supabase client with service role
		const supabase = createClient<Database>(supabaseUrl, serviceRoleKey, {
			auth: {
				autoRefreshToken: false,
				persistSession: false,
			},
		});

		console.log("📧 Creating auth user...");

		// Create user in Supabase Auth
		const { data: authData, error: authError } = await supabase.auth.admin.createUser({
			email: adminEmail,
			password: adminPassword,
			email_confirm: true,
			user_metadata: {
				first_name: firstName,
				last_name: lastName,
				role: "admin",
			},
		});

		if (authError) {
			console.error("❌ Auth error:", authError.message);
			process.exit(1);
		}

		if (!authData.user) {
			console.error("❌ Failed to create user");
			process.exit(1);
		}

		console.log("👤 Creating profile...");

		// Create profile record
		const { error: profileError } = await supabase.from("profiles").insert({
			id: authData.user.id,
			email: adminEmail,
			first_name: firstName,
			last_name: lastName,
			role: "admin",
		});

		if (profileError) {
			console.error("❌ Profile error:", profileError.message);
			// Try to clean up
			await supabase.auth.admin.deleteUser(authData.user.id);
			process.exit(1);
		}

		console.log("✅ Admin user created successfully!");
		console.log("");
		console.log("📋 Login Details:");
		console.log(`   Email: ${adminEmail}`);
		console.log(`   Password: ${adminPassword}`);
		console.log(`   Role: admin`);
		console.log("");
		console.log("🌐 You can now login at: http://localhost:3000/login");
	} catch (error) {
		console.error("❌ Unexpected error:", error);
		process.exit(1);
	}
}

// Run the script
createAdmin().catch(console.error);
