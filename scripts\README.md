# Admin User Creation Scripts

## Quick Start

### Method 1: Using npm script (Recommended)
```bash
npm run create-admin
```

### Method 2: Direct execution
```bash
node scripts/create-admin.js
```

### Method 3: TypeScript version (if you have tsx installed)
```bash
npx tsx scripts/create-admin.ts
```

## What the script does

1. **Creates Supabase Auth User** - Sets up authentication credentials
2. **Creates Profile Record** - Links user to your app's profile system
3. **Creates Employee Record** (if role is "employee") - Sets up employee-specific data

## Required Information

The script will prompt you for:
- **Email address** - Login email for the user
- **Password** - Login password 
- **First name** - User's first name
- **Last name** - User's last name
- **Role** - Either "admin" or "employee" (defaults to "admin")

## Environment Variables

Make sure these are set in your `.env.local` file:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Example Usage

```bash
$ npm run create-admin

🔐 Create Admin User for Soleil et Découverte

Email address: <EMAIL>
Password: ********
First name: John
Last name: Doe
Role (admin/employee) [admin]: admin

🔄 Creating user...
✅ Auth user created successfully
✅ Profile created successfully

🎉 User created successfully!
📧 Email: <EMAIL>
👤 Name: John Doe
🔑 Role: admin
🆔 User ID: 12345678-1234-1234-1234-123456789012

🌐 You can now login at: http://localhost:3000/admin/login
```

## Troubleshooting

### "Missing Supabase environment variables"
- Check that `.env.local` exists and contains the required variables
- Make sure variable names are exactly as shown above

### "Error creating auth user: User already registered"
- The email address is already in use
- Try a different email or delete the existing user from Supabase Dashboard

### "Error creating profile"
- Usually means the profiles table doesn't exist or has different columns
- Check your database schema matches the expected structure

## Help

Run with `--help` flag for more information:
```bash
npm run create-admin -- --help
```
